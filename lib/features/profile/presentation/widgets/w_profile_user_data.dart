// ignore_for_file: deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WProfileUserData extends StatelessWidget {
  const WProfileUserData({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        if (state.meStatus.isFailure || state.meStatus.isInProgress) {
          return Column(
            children: [
              SizedBox(
                width: 100,
                height: 100,
                child: Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(200),
                      child: Container(
                        color: AppColors.darkGrey,
                        width: 100,
                        height: 100,
                        alignment: Alignment.center,
                        child: SvgPicture.asset(AppAssets.person, width: 50, color: AppColors.white),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        context.push(AppRouter.profileEdit);
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(color: AppColors.yellow, shape: BoxShape.circle),
                        child: SvgPicture.asset(AppAssets.edit),
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(47),
            ],
          );
        } else {
          final user = state.me;
          return Column(
            children: [
              SizedBox(
                width: 100,
                height: 100,
                child: Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(200),
                      child: CachedNetworkImage(
                        imageUrl: user.picture,
                        width: 100,
                        fit: BoxFit.cover,
                        height: 100,
                        errorWidget: (context, url, error) {
                          return Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: AppColors.darkGrey,
                              borderRadius: BorderRadius.circular(100),
                            ),
                            child: SvgPicture.asset(AppAssets.person, width: 50, color: AppColors.white),
                          );
                        },
                        progressIndicatorBuilder: (context, url, progress) {
                          return CircularProgressIndicator.adaptive();
                        },
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        context.push(AppRouter.profileEdit);
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(color: AppColors.yellow, shape: BoxShape.circle),
                        child: SvgPicture.asset(AppAssets.edit),
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(10),
              Text(user.full_name, style: context.textTheme.labelLarge),
              Text(
                user.phone.formatPhoneNumber(),
                style: context.textTheme.labelMedium!.copyWith(color: AppColors.darkGrey),
              ),
              const Gap(4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.primary.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  "Profil balansi: ${user.balance.toDouble().formatAsSpaceSeparated()} so'm",
                  style: context.textTheme.labelMedium!.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          );
        }
      },
    );
  }
}
