import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/presentation/widgets/w_ball_tab_switcher.dart';
import 'package:echipta/features/ball_rating/presentation/widgets/ball_usage_bottom_sheet.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_rating_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_history_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_earning_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_promotions_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_purchase_view.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallRatingScreen extends StatefulWidget {
  const BallRatingScreen({super.key});

  @override
  State<BallRatingScreen> createState() => _BallRatingScreenState();
}

class _BallRatingScreenState extends State<BallRatingScreen> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Load initial data
    context.read<BallRatingBloc>().add(const GetCurrentUserBallInfoEvent());
    //First tab - Load general ranking for DataTable2
    context.read<BallRatingBloc>().add(const GetGeneralRankingEvent());
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabChanged(int index) {
    context.read<BallRatingBloc>().add(ChangeTabEvent(tabIndex: index));
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // Load data for the selected tab
    switch (index) {
      case 0:
        // Load general ranking for DataTable2 display
        context.read<BallRatingBloc>().add(const GetGeneralRankingEvent());
        break;
      case 1:
        context.read<BallRatingBloc>().add(const GetBallHistoryEvent());
        break;
      case 2:
        context.read<BallRatingBloc>().add(const GetBallEarningMatchesEvent());
        break;
      case 3:
        context.read<BallRatingBloc>().add(const GetBallPromotionsEvent());
        break;
    }
  }

  void _showBallPurchaseView() {
    context.read<BallRatingBloc>().add(const GetBallPackagesEvent());
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => BlocProvider.value(
            value: context.read<BallRatingBloc>(),
            child: const BallPurchaseView(),
          ),
    );
  }

  void _showBallUsageView() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BallUsageBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: RefreshIndicator.adaptive(
        onRefresh: () async {
          context.read<BallRatingBloc>().add(const GetGeneralRankingEvent());
          context.read<BallRatingBloc>().add(const GetCurrentUserBallInfoEvent());
          context.read<BallRatingBloc>().add(const GetBallHistoryEvent());
          context.read<BallRatingBloc>().add(const GetBallEarningMatchesEvent());
          context.read<BallRatingBloc>().add(const GetBallPromotionsEvent());
          context.read<BallRatingBloc>().add(const GetBallPackagesEvent());
        },
        child: CustomScrollView(
          slivers: [
            // Header with user info
            SliverAppBar(
              backgroundColor: AppColors.primary,
              expandedHeight: MediaQuery.of(context).size.height * 0.28,
              pinned: true,
              title: Text(
                LocaleKeys.fanRanking.tr(),
                style: context.textTheme.displaySmall!.copyWith(
                  color: AppColors.white,
                ),
              ),
              ///Add refresh
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(color: AppColors.primary),
                  child: BlocBuilder<ProfileBloc, ProfileState>(
                    builder: (context, profileState) {
                      return BlocBuilder<BallRatingBloc, BallRatingState>(
                        builder: (context, ballState) {
                          final user =
                              ballState.currentUser.id != 0
                                  ? ballState.currentUser
                                  : null;

                          return Padding(
                            padding: const EdgeInsets.fromLTRB(20, 90, 20, 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // User avatar and info
                                Row(
                                  children: [
                                    Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: AppColors.white,
                                          width: 5,
                                        ),
                                      ),
                                      child: ClipOval(
                                        child: CachedNetworkImage(
                                          imageUrl: profileState.me.picture,
                                          fit: BoxFit.cover,
                                          placeholder:
                                              (context, url) => Container(
                                                color: AppColors.mediumGrey,
                                                child: const Icon(
                                                  Icons.person,
                                                  color: AppColors.white,
                                                ),
                                              ),
                                          errorWidget:
                                              (context, url, error) => Container(
                                                color: AppColors.mediumGrey,
                                                child: const Icon(
                                                  Icons.person,
                                                  color: AppColors.white,
                                                ),
                                              ),
                                        ),
                                      ),
                                    ),
                                    const Gap(16),
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 15,
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              profileState.me.full_name,
                                              style: context
                                                  .textTheme
                                                  .headlineMedium!
                                                  .copyWith(
                                                    color: AppColors.white,
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 16,
                                                  ),
                                            ),
                                            const Gap(8),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                _buildStatColumn(
                                                  '${user?.position ?? '#'}',
                                                  "O'rnim",
                                                ),
                                                _buildStatColumn(
                                                  '${user?.total_balls ?? '#'}',
                                                  LocaleKeys.myBalls.tr(),
                                                ),
                                                _buildStatColumn(
                                                  '${user?.total_games ?? '#'}',
                                                  "O'yinlar",
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const Gap(24),
                                // Action buttons
                                Row(
                                  children: [
                                    Expanded(
                                      child: _buildActionButton(
                                        LocaleKeys.ballPurchase.tr(),
                                        const Color(0xFF11084A),
                                        // Figma dark blue
                                        AppColors.white,
                                        _showBallPurchaseView,
                                      ),
                                    ),
                                    const Gap(13),
                                    Expanded(
                                      child: _buildActionButton(
                                        LocaleKeys.ballUsage.tr(),
                                        const Color(0xFF11084A),
                                        // Figma dark blue
                                        AppColors.white,
                                        _showBallUsageView,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
            ),

            // Tab switcher
            SliverToBoxAdapter(
              child: BlocBuilder<BallRatingBloc, BallRatingState>(
                builder: (context, state) {
                  return WBallTabSwitcher(
                    currentIndex: state.currentTabIndex,
                    onTabChanged: _onTabChanged,
                  );
                },
              ),
            ),

            // Content based on selected tab
            SliverToBoxAdapter(
              child: Container(
                height: MediaQuery.of(context).size.height -
                       MediaQuery.of(context).size.height * 0.28 - // SliverAppBar height
                       55 - // Tab switcher height
                       MediaQuery.of(context).padding.top, // Status bar
                child: BlocBuilder<BallRatingBloc, BallRatingState>(
                  builder: (context, state) {
                    return PageView(
                      controller: _pageController,
                      onPageChanged: (index) {
                        context.read<BallRatingBloc>().add(
                          ChangeTabEvent(tabIndex: index),
                        );
                      },
                      children: [
                        BallRatingView(),
                        Padding(
                          padding: EdgeInsets.only(bottom: context.padding.bottom),
                          child: BallHistoryView(),
                        ),
                        Padding(
                          padding: EdgeInsets.only(bottom: context.padding.bottom),
                          child: BallEarningView(),
                        ),
                        Padding(
                          padding: EdgeInsets.only(bottom: context.padding.bottom),
                          child: BallPromotionsView(),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatColumn(String value, String label) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          value,
          style: context.textTheme.displayMedium!.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
          textAlign: TextAlign.center,
        ),
        const Gap(6),
        Text(
          label,
          style: context.textTheme.bodySmall!.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
            fontSize: 10,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String text,
    Color backgroundColor,
    Color textColor,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 43,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            text,
            style: context.textTheme.bodyMedium!.copyWith(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }
}
