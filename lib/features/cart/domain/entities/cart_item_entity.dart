// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';

class CartItemEntity extends Equatable {
  final ProductEntity product;
  final int quantity;

  const CartItemEntity({
    required this.product,
    this.quantity = 1,
  });

  @override
  List<Object?> get props => [product, quantity];

  CartItemEntity copyWith({
    ProductEntity? product,
    int? quantity,
  }) {
    return CartItemEntity(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
    );
  }

  // Calculate total price for this cart item
  int get totalPrice => product.price * quantity;

  // Convert to API format for basket price calculation
  Map<String, dynamic> toApiJson() {
    return {
      'product_id': product.id,
      'count': quantity,
    };
  }
}
