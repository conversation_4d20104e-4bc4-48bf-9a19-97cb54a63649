// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class BasketPriceEntity extends Equatable {
  final int totalPrice;

  const BasketPriceEntity({
    this.totalPrice = 0,
  });

  @override
  List<Object?> get props => [totalPrice];

  BasketPriceEntity copyWith({
    int? totalPrice,
  }) {
    return BasketPriceEntity(
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}
