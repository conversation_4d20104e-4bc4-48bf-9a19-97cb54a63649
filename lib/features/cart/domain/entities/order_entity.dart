// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

enum DeliveryType { inStore, delivery }

enum PaymentType { alif }

class OrderEntity extends Equatable {
  final int orderId;
  final String? paymentUrl;

  const OrderEntity({
    this.orderId = 0,
    this.paymentUrl,
  });

  @override
  List<Object?> get props => [orderId, paymentUrl];

  OrderEntity copyWith({
    int? orderId,
    String? paymentUrl,
  }) {
    return OrderEntity(
      orderId: orderId ?? this.orderId,
      paymentUrl: paymentUrl ?? this.paymentUrl,
    );
  }
}

extension DeliveryTypeExtension on DeliveryType {
  String get apiValue {
    switch (this) {
      case DeliveryType.inStore:
        return 'in_store';
      case DeliveryType.delivery:
        return 'delivery';
    }
  }

  String get displayName {
    switch (this) {
      case DeliveryType.inStore:
        return 'Olib ketish';
      case DeliveryType.delivery:
        return 'Yetkazib berish';
    }
  }
}

extension PaymentTypeExtension on PaymentType {
  String get apiValue {
    switch (this) {
      case PaymentType.alif:
        return 'alif';
    }
  }

  String get displayName {
    switch (this) {
      case PaymentType.alif:
        return 'Alif balance';
    }
  }
}
