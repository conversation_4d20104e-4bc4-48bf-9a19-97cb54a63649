import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';

abstract class CartRepository {
  Future<Either<Failure, BasketPriceEntity>> getBasketPrice(
    List<CartItemEntity> items,
    DeliveryType deliveryType,
  );

  Future<Either<Failure, OrderEntity>> createOrder(
    List<CartItemEntity> items,
    DeliveryType deliveryType,
    String? deliveryAddress,
    PaymentType paymentType,
  );

  Future<Either<Failure, OrderStatusEntity>> getOrderStatus(int orderId);
}
