part of 'cart_bloc.dart';

abstract class CartEvent extends Equatable {
  const CartEvent();

  @override
  List<Object?> get props => [];
}

class AddToCart extends CartEvent {
  final ProductEntity product;
  final int? quantity;

  const AddToCart({required this.product, this.quantity});

  @override
  List<Object?> get props => [product, quantity];
}

class RemoveFromCart extends CartEvent {
  final int productId;

  const RemoveFromCart({required this.productId});

  @override
  List<Object?> get props => [productId];
}

class UpdateQuantity extends CartEvent {
  final int productId;
  final int quantity;

  const UpdateQuantity({required this.productId, required this.quantity});

  @override
  List<Object?> get props => [productId, quantity];
}

class ClearCart extends CartEvent {
  const ClearCart();
}

class SetDeliveryType extends CartEvent {
  final DeliveryType deliveryType;

  const SetDeliveryType({required this.deliveryType});

  @override
  List<Object?> get props => [deliveryType];
}

class SetDeliveryAddress extends CartEvent {
  final String address;

  const SetDeliveryAddress({required this.address});

  @override
  List<Object?> get props => [address];
}

class GetBasketPrice extends CartEvent {
  const GetBasketPrice();
}

class CreateOrder extends CartEvent {
  const CreateOrder();
}

class GetOrderStatus extends CartEvent {
  final int orderId;

  const GetOrderStatus({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class AddOrderToHistory extends CartEvent {
  final OrderEntity order;

  const AddOrderToHistory({required this.order});

  @override
  List<Object?> get props => [order];
}
