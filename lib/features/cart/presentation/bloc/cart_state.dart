part of 'cart_bloc.dart';

class CartState extends Equatable {
  final List<CartItemEntity> cartItems;
  final DeliveryType deliveryType;
  final String? deliveryAddress;
  final BasketPriceEntity? basketPrice;
  final OrderEntity? currentOrder;
  final OrderStatusEntity? currentOrderStatus;
  final List<OrderEntity> orderHistory;
  final FormzSubmissionStatus basketPriceStatus;
  final FormzSubmissionStatus orderStatus;
  final FormzSubmissionStatus orderStatusCheckStatus;

  const CartState({
    this.cartItems = const [],
    this.deliveryType = DeliveryType.inStore,
    this.deliveryAddress,
    this.basketPrice,
    this.currentOrder,
    this.currentOrderStatus,
    this.orderHistory = const [],
    this.basketPriceStatus = FormzSubmissionStatus.initial,
    this.orderStatus = FormzSubmissionStatus.initial,
    this.orderStatusCheckStatus = FormzSubmissionStatus.initial,
  });

  @override
  List<Object?> get props => [
        cartItems,
        deliveryType,
        deliveryAddress,
        basketPrice,
        currentOrder,
        currentOrderStatus,
        orderHistory,
        basketPriceStatus,
        orderStatus,
        orderStatusCheckStatus,
      ];

  CartState copyWith({
    List<CartItemEntity>? cartItems,
    DeliveryType? deliveryType,
    String? deliveryAddress,
    BasketPriceEntity? basketPrice,
    OrderEntity? currentOrder,
    OrderStatusEntity? currentOrderStatus,
    List<OrderEntity>? orderHistory,
    FormzSubmissionStatus? basketPriceStatus,
    FormzSubmissionStatus? orderStatus,
    FormzSubmissionStatus? orderStatusCheckStatus,
  }) {
    return CartState(
      cartItems: cartItems ?? this.cartItems,
      deliveryType: deliveryType ?? this.deliveryType,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      basketPrice: basketPrice ?? this.basketPrice,
      currentOrder: currentOrder ?? this.currentOrder,
      currentOrderStatus: currentOrderStatus ?? this.currentOrderStatus,
      orderHistory: orderHistory ?? this.orderHistory,
      basketPriceStatus: basketPriceStatus ?? this.basketPriceStatus,
      orderStatus: orderStatus ?? this.orderStatus,
      orderStatusCheckStatus: orderStatusCheckStatus ?? this.orderStatusCheckStatus,
    );
  }

  // Helper getters
  int get totalItems => cartItems.fold(0, (sum, item) => sum + item.quantity);
  
  int get totalPrice => cartItems.fold(0, (sum, item) => sum + item.totalPrice);
  
  bool get isEmpty => cartItems.isEmpty;
  
  bool get isNotEmpty => cartItems.isNotEmpty;
  
  bool get requiresDeliveryAddress => deliveryType == DeliveryType.delivery;
  
  bool get canCreateOrder => isNotEmpty && (!requiresDeliveryAddress || (deliveryAddress?.isNotEmpty ?? false));
}
