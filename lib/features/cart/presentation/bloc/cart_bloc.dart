import 'package:bloc/bloc.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';
import 'package:echipta/features/cart/domain/usecases/create_order_use_case.dart';
import 'package:echipta/features/cart/domain/usecases/get_basket_price_use_case.dart';
import 'package:echipta/features/cart/domain/usecases/get_order_status_use_case.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'cart_event.dart';
part 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  final GetBasketPriceUseCase _getBasketPriceUseCase = GetBasketPriceUseCase();
  final CreateOrderUseCase _createOrderUseCase = CreateOrderUseCase();
  final GetOrderStatusUseCase _getOrderStatusUseCase = GetOrderStatusUseCase();

  CartBloc() : super(const CartState()) {
    on<AddToCart>(_onAddToCart);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<UpdateQuantity>(_onUpdateQuantity);
    on<ClearCart>(_onClearCart);
    on<SetDeliveryType>(_onSetDeliveryType);
    on<SetDeliveryAddress>(_onSetDeliveryAddress);
    on<GetBasketPrice>(_onGetBasketPrice);
    on<CreateOrder>(_onCreateOrder);
    on<GetOrderStatus>(_onGetOrderStatus);
    on<AddOrderToHistory>(_onAddOrderToHistory);
  }

  void _onAddToCart(AddToCart event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);

    // Check if product already exists in cart
    final existingIndex = updatedItems.indexWhere((item) => item.product.id == event.product.id);

    if (existingIndex != -1) {
      // Update quantity if product exists
      final existingItem = updatedItems[existingIndex];
      updatedItems[existingIndex] = existingItem.copyWith(
        quantity: existingItem.quantity + (event.quantity ?? 1),
      );
    } else {
      // Add new item to cart
      updatedItems.add(CartItemEntity(product: event.product, quantity: event.quantity ?? 1));
    }

    emit(state.copyWith(cartItems: updatedItems));
  }

  void _onRemoveFromCart(RemoveFromCart event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);
    updatedItems.removeWhere((item) => item.product.id == event.productId);
    
    emit(state.copyWith(cartItems: updatedItems));
  }

  void _onUpdateQuantity(UpdateQuantity event, Emitter<CartState> emit) {
    final List<CartItemEntity> updatedItems = List.from(state.cartItems);
    final index = updatedItems.indexWhere((item) => item.product.id == event.productId);
    
    if (index != -1) {
      if (event.quantity <= 0) {
        updatedItems.removeAt(index);
      } else {
        updatedItems[index] = updatedItems[index].copyWith(quantity: event.quantity);
      }
    }
    
    emit(state.copyWith(cartItems: updatedItems));
  }

  void _onClearCart(ClearCart event, Emitter<CartState> emit) {
    emit(state.copyWith(cartItems: []));
  }

  void _onSetDeliveryType(SetDeliveryType event, Emitter<CartState> emit) {
    emit(state.copyWith(deliveryType: event.deliveryType));
  }

  void _onSetDeliveryAddress(SetDeliveryAddress event, Emitter<CartState> emit) {
    emit(state.copyWith(deliveryAddress: event.address));
  }

  void _onGetBasketPrice(GetBasketPrice event, Emitter<CartState> emit) async {
    if (state.cartItems.isEmpty) return;
    
    emit(state.copyWith(basketPriceStatus: FormzSubmissionStatus.inProgress));
    
    final result = await _getBasketPriceUseCase(GetBasketPriceParams(
      items: state.cartItems,
      deliveryType: state.deliveryType,
    ));
    
    if (result.isRight) {
      emit(state.copyWith(
        basketPrice: result.right,
        basketPriceStatus: FormzSubmissionStatus.success,
      ));
    } else {
      emit(state.copyWith(basketPriceStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onCreateOrder(CreateOrder event, Emitter<CartState> emit) async {
    if (state.cartItems.isEmpty) return;
    
    emit(state.copyWith(orderStatus: FormzSubmissionStatus.inProgress));
    
    final result = await _createOrderUseCase(CreateOrderParams(
      items: state.cartItems,
      deliveryType: state.deliveryType,
      deliveryAddress: state.deliveryAddress,
      paymentType: PaymentType.alif,
    ));
    
    if (result.isRight) {
      emit(state.copyWith(
        currentOrder: result.right,
        orderStatus: FormzSubmissionStatus.success,
      ));
    } else {
      emit(state.copyWith(orderStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onGetOrderStatus(GetOrderStatus event, Emitter<CartState> emit) async {
    emit(state.copyWith(orderStatusCheckStatus: FormzSubmissionStatus.inProgress));
    
    final result = await _getOrderStatusUseCase(IdParam(id: event.orderId));
    
    if (result.isRight) {
      emit(state.copyWith(
        currentOrderStatus: result.right,
        orderStatusCheckStatus: FormzSubmissionStatus.success,
      ));
    } else {
      emit(state.copyWith(orderStatusCheckStatus: FormzSubmissionStatus.failure));
    }
  }

  void _onAddOrderToHistory(AddOrderToHistory event, Emitter<CartState> emit) {
    final List<OrderEntity> updatedHistory = List.from(state.orderHistory);
    updatedHistory.insert(0, event.order);
    
    emit(state.copyWith(orderHistory: updatedHistory));
  }
}
