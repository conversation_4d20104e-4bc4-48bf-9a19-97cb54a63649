import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_item.dart';
import 'package:echipta/features/cart/presentation/widgets/w_delivery_options.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_summary.dart';
import 'package:echipta/features/cart/presentation/widgets/w_payment_handler.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:formz/formz.dart';

class WCartTab extends StatelessWidget {
  const WCartTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        if (state.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  size: 80,
                  color: AppColors.grey,
                ),
                Gap(16),
                Text(
                  'Savat bo\'sh',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey,
                  ),
                ),
                Gap(8),
                Text(
                  'Mahsulotlarni qo\'shish uchun\nbosh sahifaga o\'ting',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cart Items
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.cartItems.length,
                      separatorBuilder: (context, index) => const Gap(12),
                      itemBuilder: (context, index) {
                        final item = state.cartItems[index];
                        return WCartItem(item: item);
                      },
                    ),
                    const Gap(24),
                    
                    // Delivery Options
                    const WDeliveryOptions(),
                    const Gap(24),
                    
                    // Cart Summary
                    const WCartSummary(),
                  ],
                ),
              ),
            ),
            
            // Bottom Action Button
            Container(
              margin: const EdgeInsets.all( 16),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12)),
                color: AppColors.primary,
                border: Border(
                  top: BorderSide(color: AppColors.lightGrey, width: 1),
                ),
              ),
              child: BlocListener<CartBloc, CartState>(
                listener: (context, state) {
                  if (state.orderStatus == FormzSubmissionStatus.success) {
                    // Navigate to payment or show success
                    if (state.currentOrder?.paymentUrl != null) {
                      // Add order to history
                      context.read<CartBloc>().add(
                        AddOrderToHistory(order: state.currentOrder!),
                      );

                      // Navigate to payment
                      showPaymentHandler(
                        context,
                        state.currentOrder!.paymentUrl!,
                        state.currentOrder!.orderId,
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Buyurtma muvaffaqiyatli yaratildi!'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                      // Clear cart for pickup orders
                      context.read<CartBloc>().add(const ClearCart());
                    }
                  } else if (state.orderStatus == FormzSubmissionStatus.failure) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Buyurtma yaratishda xatolik yuz berdi'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                },
                child: WButton(
                  onTap: state.canCreateOrder
                      ? () {
                          context.read<CartBloc>().add(const CreateOrder());
                        }
                      : () {},
                  isLoading: state.orderStatus == FormzSubmissionStatus.inProgress,
                  txt: 'Buyurtma berish',
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
