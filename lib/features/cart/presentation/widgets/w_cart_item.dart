import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class WCartItem extends StatelessWidget {
  const WCartItem({super.key, required this.item});

  final CartItemEntity item;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.dark.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Product Image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedNetworkImage(
              imageUrl: item.product.image,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 80,
                height: 80,
                color: AppColors.mediumGrey,
                child: const Icon(
                  Icons.image,
                  color: AppColors.grey,
                ),
              ),
              errorWidget: (context, url, error) => Container(
                width: 80,
                height: 80,
                color: AppColors.mediumGrey,
                child: const Icon(
                  Icons.broken_image,
                  color: AppColors.grey,
                ),
              ),
            ),
          ),
          const Gap(12),
          
          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.title_uz,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.dark,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Gap(4),
                Text(
                  item.product.description_uz,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.grey,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Gap(8),
                Text(
                  '${item.product.price.formatPrice()} so\'m',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          const Gap(12),
          
          // Quantity Controls
          Column(
            children: [
              // Remove Button
              Row(
                children: [
                  Text(
                    '${item.totalPrice.formatPrice()} so\'m',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                  const Gap(4),
                  GestureDetector(
                    onTap: () {
                      context.read<CartBloc>().add(
                        RemoveFromCart(productId: item.product.id),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: const Icon(
                        Icons.delete_outline,
                        color: AppColors.error,
                        size: 20,
                      ),
                    ),
                  ),

                ],
              ),
              const Gap(8),
              
              // Quantity Controls
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (item.quantity > 1) {
                        context.read<CartBloc>().add(
                          UpdateQuantity(
                            productId: item.product.id,
                            quantity: item.quantity - 1,
                          ),
                        );
                      }
                    },
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.mediumGrey,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.remove,
                        size: 16,
                        color: AppColors.dark,
                      ),
                    ),
                  ),
                  const Gap(8),
                  Text(
                    '${item.quantity}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.dark,
                    ),
                  ),
                  const Gap(8),
                  GestureDetector(
                    onTap: () {
                      context.read<CartBloc>().add(
                        UpdateQuantity(
                          productId: item.product.id,
                          quantity: item.quantity + 1,
                        ),
                      );
                    },
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.add,
                        size: 16,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
