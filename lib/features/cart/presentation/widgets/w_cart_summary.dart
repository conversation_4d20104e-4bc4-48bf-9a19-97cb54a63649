import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:formz/formz.dart';

import '../../domain/entities/cart_item_entity.dart';

class WCartSummary extends StatefulWidget {
  const WCartSummary({super.key});

  @override
  State<WCartSummary> createState() => _WCartSummaryState();
}

class _WCartSummaryState extends State<WCartSummary> {
  DeliveryType? _lastDeliveryType;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        // Only trigger basket price calculation when delivery type changes to delivery
        // Don't call API for every cart item change - use local cart total instead
        if (state.cartItems.isNotEmpty &&
            state.deliveryType == DeliveryType.delivery &&
            _lastDeliveryType != state.deliveryType) {

          _lastDeliveryType = state.deliveryType;

          // Use addPostFrameCallback to avoid calling during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              context.read<CartBloc>().add(const GetBasketPrice());
            }
          });
        } else if (state.deliveryType == DeliveryType.inStore) {
          // Reset delivery type tracking when switching to in-store
          _lastDeliveryType = state.deliveryType;
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.dark.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Buyurtma xulosasi',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dark,
                ),
              ),
              const Gap(16),
              
              // Items Count
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Mahsulotlar soni:',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.grey,
                    ),
                  ),
                  Text(
                    '${state.totalItems} ta',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.dark,
                    ),
                  ),
                ],
              ),
              const Gap(8),
              
              // Subtotal
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Mahsulotlar narxi:',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.grey,
                    ),
                  ),
                  Text(
                    '${state.totalPrice.formatPrice()} so\'m',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.dark,
                    ),
                  ),
                ],
              ),
              
              // Delivery Fee (if applicable)
              if (state.deliveryType == DeliveryType.delivery) ...[
                const Gap(8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Yetkazib berish:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.grey,
                      ),
                    ),
                    Text(
                      state.basketPriceStatus == FormzSubmissionStatus.inProgress
                          ? 'Hisoblanmoqda...'
                          : state.basketPrice != null && state.basketPrice!.totalPrice > 0
                              ? '${(state.basketPrice!.totalPrice - state.totalPrice).formatPrice()} so\'m'
                              : 'Bepul',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.dark,
                      ),
                    ),
                  ],
                ),
              ],
              
              const Gap(12),
              const Divider(color: AppColors.mediumGrey),
              const Gap(12),
              
              // Total
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Umumiy narx:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.dark,
                    ),
                  ),
                  Text(
                    // Use server total only when delivery is selected and API call succeeded
                    // Otherwise use local cart total
                    state.deliveryType == DeliveryType.delivery &&
                            state.basketPrice != null &&
                            state.basketPrice!.totalPrice > 0 &&
                            state.basketPriceStatus == FormzSubmissionStatus.success
                        ? '${state.basketPrice!.totalPrice.formatPrice()} so\'m'
                        : '${state.totalPrice.formatPrice()} so\'m',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              
              // Payment Method
              const Gap(16),
              const Divider(color: AppColors.mediumGrey),
              const Gap(16),
              const Text(
                'To\'lov usuli',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.dark,
                ),
              ),
              const Gap(8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.mediumGrey,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.account_balance_wallet_outlined,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    Gap(8),
                    Text(
                      'Alif balance',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.dark,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }


}
