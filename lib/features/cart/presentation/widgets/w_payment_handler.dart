import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:gap/gap.dart';
import 'package:url_launcher/url_launcher.dart';

class WPaymentHandler extends StatefulWidget {
  const WPaymentHandler({
    super.key,
    required this.paymentUrl,
    required this.orderId,
  });

  final String paymentUrl;
  final int orderId;

  @override
  State<WPaymentHandler> createState() => _WPaymentHandlerState();
}

class _WPaymentHandlerState extends State<WPaymentHandler> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    try {
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              print("🌐 WebView started loading: $url");
              setState(() {
                _isLoading = true;
              });
            },
            onPageFinished: (String url) {
              print("✅ WebView finished loading: $url");
              setState(() {
                _isLoading = false;
              });

              // Check if payment is completed based on URL
              if (url.contains('success') || url.contains('completed')) {
                _handlePaymentSuccess();
              } else if (url.contains('cancel') || url.contains('error')) {
                _handlePaymentError();
              }
            },
            onWebResourceError: (WebResourceError error) {
              print("❌ WebView error: ${error.description}");
              setState(() {
                _isLoading = false;
              });
              _handlePaymentError();
            },
            onNavigationRequest: (NavigationRequest request) {
              print("🔄 Navigation request: ${request.url}");
              return NavigationDecision.navigate;
            },
          ),
        );

      // Add timeout protection
      Future.delayed(const Duration(seconds: 30), () {
        if (_isLoading && mounted) {
          print("⏰ WebView loading timeout");
          setState(() {
            _isLoading = false;
          });
          _handlePaymentError();
        }
      });

      // Load the URL
      print("🚀 Loading payment URL: ${widget.paymentUrl}");
      _controller.loadRequest(Uri.parse(widget.paymentUrl));

    } catch (e) {
      print("💥 WebView initialization error: $e");
      _handlePaymentError();
    }
  }

  void _handlePaymentSuccess() {
    if (!mounted) return;

    // Check order status to confirm payment
    context.read<CartBloc>().add(GetOrderStatus(orderId: widget.orderId));

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('To\'lov muvaffaqiyatli amalga oshirildi!'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );

    // Clear cart and navigate back
    context.read<CartBloc>().add(const ClearCart());
    Navigator.of(context).pop();
  }

  void _handlePaymentError() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('To\'lovda xatolik yuz berdi'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: const Text(
          'To\'lov',
          style: TextStyle(
            color: AppColors.dark,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppColors.dark),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  ),
                  Gap(16),
                  Text(
                    'To\'lov sahifasi yuklanmoqda...',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.grey,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}

// Helper function to show payment handler
void showPaymentHandler(BuildContext context, String paymentUrl, int orderId) {
  // Try WebView first, with fallback to external browser
  try {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WPaymentHandler(
          paymentUrl: paymentUrl,
          orderId: orderId,
        ),
      ),
    );
  } catch (e) {
    print("❌ WebView failed, using external browser: $e");
    _openInExternalBrowser(context, paymentUrl, orderId);
  }
}

// Fallback function to open payment in external browser
void _openInExternalBrowser(BuildContext context, String paymentUrl, int orderId) async {
  try {
    final Uri url = Uri.parse(paymentUrl);

    // Show confirmation dialog first
    final bool? shouldLaunch = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('To\'lov'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('To\'lov sahifasi tashqi brauzerda ochiladi.'),
            const SizedBox(height: 16),
            Text(
              'Buyurtma ID: $orderId',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Bekor qilish'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('To\'lov qilish'),
          ),
        ],
      ),
    );

    if (shouldLaunch == true) {
      // Launch the URL in external browser
      final bool launched = await launchUrl(
        url,
        mode: LaunchMode.externalApplication,
      );

      if (launched) {
        print("✅ Payment URL launched in external browser");
        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('To\'lov sahifasi ochildi. To\'lovni yakunlangandan so\'ng ilovaga qayting.'),
              backgroundColor: AppColors.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else {
        throw Exception('Could not launch URL');
      }
    }
  } catch (e) {
    print("❌ External browser launch failed: $e");
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('To\'lov sahifasini ochishda xatolik'),
          backgroundColor: AppColors.primary,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }
}
