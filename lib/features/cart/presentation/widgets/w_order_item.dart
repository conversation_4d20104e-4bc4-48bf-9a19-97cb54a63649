import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class WOrderItem extends StatelessWidget {
  const WOrderItem({super.key, required this.order});

  final OrderEntity order;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.dark.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Buyurtma #${order.orderId}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dark,
                ),
              ),
              GestureDetector(
                onTap: () {
                  context.read<CartBloc>().add(
                    GetOrderStatus(orderId: order.orderId),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Text(
                    'Holat tekshirish',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Gap(12),
          
          // Order Status
          BlocBuilder<CartBloc, CartState>(
            builder: (context, state) {
              if (state.currentOrderStatus != null && 
                  state.currentOrderStatus!.status != 0) {
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getStatusColor(state.currentOrderStatus!.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getStatusIcon(state.currentOrderStatus!.status),
                        color: _getStatusColor(state.currentOrderStatus!.status),
                        size: 20,
                      ),
                      const Gap(8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              state.currentOrderStatus!.statusText,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: _getStatusColor(state.currentOrderStatus!.status),
                              ),
                            ),
                            if (state.currentOrderStatus!.info.isNotEmpty)
                              Text(
                                state.currentOrderStatus!.info,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: _getStatusColor(state.currentOrderStatus!.status),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          
          const Gap(12),
          
          // Payment URL (if available)
          if (order.paymentUrl != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.payment,
                    color: AppColors.warning,
                    size: 20,
                  ),
                  const Gap(8),
                  const Expanded(
                    child: Text(
                      'To\'lov kutilmoqda',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.warning,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      // Handle payment URL navigation
                      // You can implement WebView or external browser opening here
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.warning,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Text(
                        'To\'lash',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(int status) {
    switch (status) {
      case 0:
      case 1:
        return AppColors.warning;
      case 2:
      case 3:
        return AppColors.success;
      case -1:
        return AppColors.error;
      default:
        return AppColors.grey;
    }
  }

  IconData _getStatusIcon(int status) {
    switch (status) {
      case 0:
      case 1:
        return Icons.hourglass_empty;
      case 2:
        return Icons.check_circle;
      case 3:
        return Icons.local_shipping;
      case -1:
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }
}
