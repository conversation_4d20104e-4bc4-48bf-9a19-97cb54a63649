import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/features/cart/presentation/widgets/w_order_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class WOrdersTab extends StatelessWidget {
  const WOrdersTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        if (state.orderHistory.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 80,
                  color: AppColors.grey,
                ),
                Gap(16),
                Text(
                  'Buyurtmalar yo\'q',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.grey,
                  ),
                ),
                Gap(8),
                Text(
                  'Hali hech qanday buyurtma\nbermagansiz',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: state.orderHistory.length,
          separatorBuilder: (context, index) => const Gap(12),
          itemBuilder: (context, index) {
            final order = state.orderHistory[index];
            return WOrderItem(order: order);
          },
        );
      },
    );
  }
}
