import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/cart/data/models/basket_price_model.dart';
import 'package:echipta/features/cart/data/models/order_model.dart';
import 'package:echipta/features/cart/data/models/order_status_model.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';

abstract class CartDatasource {
  Future<BasketPriceModel> getBasketPrice(List<CartItemEntity> items, DeliveryType deliveryType);
  Future<OrderModel> createOrder(List<CartItemEntity> items, DeliveryType deliveryType, String? deliveryAddress, PaymentType paymentType);
  Future<OrderStatusModel> getOrderStatus(int orderId);
}

class CartDatasourceImpl implements CartDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;

  @override
  Future<BasketPriceModel> getBasketPrice(List<CartItemEntity> items, DeliveryType deliveryType) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "items": items.map((item) => item.toApiJson()).toList(),
        "delivery_type": deliveryType.apiValue,
      };
      
      final response = await _dio.post(
        "/products/get-basket-price",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return BasketPriceModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<OrderModel> createOrder(List<CartItemEntity> items, DeliveryType deliveryType, String? deliveryAddress, PaymentType paymentType) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "items": items.map((item) => item.toApiJson()).toList(),
        "delivery_type": deliveryType.apiValue,
        "payment_type": paymentType.apiValue,
      };
      
      // Add delivery address if delivery type is delivery
      if (deliveryType == DeliveryType.delivery && deliveryAddress != null) {
        data["delivery_address"] = deliveryAddress;
      }
      
      final response = await _dio.post(
        "/products/create-order",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return OrderModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<OrderStatusModel> getOrderStatus(int orderId) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      
      final response = await _dio.get(
        "/games/order-status",
        queryParameters: {"order_id": orderId},
        options: Options(headers: {"Authorization": token}),
      );
      
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return OrderStatusModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
