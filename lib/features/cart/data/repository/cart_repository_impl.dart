import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/cart/data/datasource/cart_datasource.dart';
import 'package:echipta/features/cart/domain/entities/basket_price_entity.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/domain/entities/order_status_entity.dart';
import 'package:echipta/features/cart/domain/repository/cart_repository.dart';

class CartRepositoryImpl implements CartRepository {
  final CartDatasource _datasource = serviceLocator<CartDatasourceImpl>();

  @override
  Future<Either<Failure, BasketPriceEntity>> getBasketPrice(
    List<CartItemEntity> items,
    DeliveryType deliveryType,
  ) async {
    try {
      final result = await _datasource.getBasketPrice(items, deliveryType);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, OrderEntity>> createOrder(
    List<CartItemEntity> items,
    DeliveryType deliveryType,
    String? deliveryAddress,
    PaymentType paymentType,
  ) async {
    try {
      final result = await _datasource.createOrder(items, deliveryType, deliveryAddress, paymentType);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, OrderStatusEntity>> getOrderStatus(int orderId) async {
    try {
      final result = await _datasource.getOrderStatus(orderId);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
